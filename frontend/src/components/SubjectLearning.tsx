import React, { useState, useEffect } from 'react';
import axios from 'axios';
import TopicTree from './TopicTree';
import ContentDisplay from './ContentDisplay';
import { useLanguage } from '../contexts/LanguageContext';
import styles from './SubjectLearning.module.css';

function SubjectLearning({ subject, onBack, apiKey }) {
  const { selectedLanguage } = useLanguage();
  const [curriculum, setCurriculum] = useState(null);
  const [selectedTopic, setSelectedTopic] = useState(null);
  const [courseContent, setCourseContent] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingCurriculum, setIsLoadingCurriculum] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchCurriculum();
  }, [subject.id]);

  const fetchCurriculum = async () => {
    try {
      setIsLoadingCurriculum(true);
      const response = await axios.get(`http://localhost:3001/api/curricula/${subject.id}`);
      setCurriculum(response.data.structure.curriculum);
      setError(null);
    } catch (error) {
      console.error('Error fetching curriculum:', error);
      setError('Failed to load curriculum. Please try again.');
    } finally {
      setIsLoadingCurriculum(false);
    }
  };

  const handleTopicSelect = (topic) => {
    setSelectedTopic(topic);
    setCourseContent(''); // Clear previous content when selecting a new topic
  };

  const handleGenerateCourse = async () => {
    if (!selectedTopic) {
      alert('Please select a topic first');
      return;
    }
    
    if (!apiKey) {
      alert('Please set your API key in the settings');
      return;
    }

    setIsLoading(true);
    try {
      const response = await axios.post('http://localhost:3001/api/generate', {
        topic: selectedTopic.title,
        subjectId: subject.id,
        topicId: selectedTopic.id,
        apiKey: apiKey,
        language: selectedLanguage.name
      });
      
      setCourseContent(response.data.content);
    } catch (error) {
      console.error('Error generating course:', error);
      if (error.response) {
        alert(`Error: ${error.response.data.error || 'Unknown error'}`);
      } else if (error.request) {
        alert('Error: No response from server. Make sure the backend is running.');
      } else {
        alert(`Error: ${error.message}`);
      }
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoadingCurriculum) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.spinner}></div>
        <p className={styles.loadingText}>Loading curriculum...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.errorContainer}>
        <h2>Error</h2>
        <p>{error}</p>
        <div className={styles.errorActions}>
          <button onClick={fetchCurriculum} className={styles.retryButton}>
            Try Again
          </button>
          <button onClick={onBack} className={styles.backButton}>
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <button onClick={onBack} className={styles.backButton}>
          ← Back to Dashboard
        </button>
        <div className={styles.subjectInfo}>
          <h1>{subject.title}</h1>
          {subject.description && <p>{subject.description}</p>}
        </div>
      </div>

      <div className={styles.mainContent}>
        <div className={styles.sidebar}>
          <TopicTree 
            curriculum={curriculum} 
            onTopicSelect={handleTopicSelect}
            selectedTopic={selectedTopic}
            subjectTitle={subject.title}
          />
        </div>
        
        <div className={styles.contentArea}>
          {selectedTopic && (
            <div className={styles.generateSection}>
              <button 
                className={styles.generateButton}
                onClick={handleGenerateCourse}
                disabled={isLoading}
              >
                {isLoading ? 'Generating...' : 'Generate New Course'}
              </button>
            </div>
          )}
          <ContentDisplay 
            content={courseContent} 
            isLoading={isLoading}
            selectedTopic={selectedTopic}
          />
        </div>
      </div>
    </div>
  );
}

export default SubjectLearning;
